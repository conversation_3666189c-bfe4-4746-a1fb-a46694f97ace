#!/bin/bash

# Script to import the new Appointment Metrics Dashboard into Grafana

set -e

# Configuration
GRAFANA_URL="http://localhost:3000"
GRAFANA_USER="admin"
GRAFANA_PASSWORD="admin"
DASHBOARD_FILE="appointment-metrics-dashboard.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check if Grafana is running
check_grafana() {
    print_info "Checking if Grafana is running..."
    if curl -s "$GRAFANA_URL/api/health" > /dev/null 2>&1; then
        print_success "Grafana is running at $GRAFANA_URL"
    else
        print_error "Grafana is not accessible at $GRAFANA_URL"
        print_info "Please make sure Grafana is running with: docker-compose -f docker-compose.monitoring.yml up -d"
        exit 1
    fi
}

# Check if dashboard file exists
check_dashboard_file() {
    print_info "Checking dashboard file..."
    if [ ! -f "$DASHBOARD_FILE" ]; then
        print_error "Dashboard file '$DASHBOARD_FILE' not found"
        exit 1
    fi
    print_success "Dashboard file found: $DASHBOARD_FILE"
}

# Import dashboard
import_dashboard() {
    print_info "Importing new Appointment Metrics Dashboard..."
    
    # Create the import payload
    IMPORT_PAYLOAD=$(cat "$DASHBOARD_FILE" | jq '{
        dashboard: .dashboard,
        overwrite: true
    }')
    
    # Import the dashboard
    RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -u "$GRAFANA_USER:$GRAFANA_PASSWORD" \
        -d "$IMPORT_PAYLOAD" \
        "$GRAFANA_URL/api/dashboards/import")
    
    # Check if import was successful
    if echo "$RESPONSE" | jq -e '.uid' > /dev/null 2>&1; then
        DASHBOARD_UID=$(echo "$RESPONSE" | jq -r '.uid')
        DASHBOARD_URL=$(echo "$RESPONSE" | jq -r '.importedUrl // .url // ""')
        
        print_success "Dashboard imported successfully!"
        print_info "Dashboard UID: $DASHBOARD_UID"
        print_info "Dashboard URL: $GRAFANA_URL$DASHBOARD_URL"
    else
        print_error "Failed to import dashboard"
        print_error "Response: $RESPONSE"
        exit 1
    fi
}

# Configure Prometheus datasource if needed
configure_datasource() {
    print_info "Checking Prometheus datasource..."
    
    # Check if Prometheus datasource exists
    DATASOURCE_CHECK=$(curl -s -u "$GRAFANA_USER:$GRAFANA_PASSWORD" \
        "$GRAFANA_URL/api/datasources/name/prometheus" 2>/dev/null || echo "not found")
    
    if echo "$DATASOURCE_CHECK" | grep -q "not found"; then
        print_warning "Prometheus datasource not found, creating it..."
        
        # Create Prometheus datasource
        DATASOURCE_PAYLOAD='{
            "name": "prometheus",
            "type": "prometheus",
            "url": "http://prometheus:9090",
            "access": "proxy",
            "isDefault": true,
            "basicAuth": false
        }'
        
        DATASOURCE_RESPONSE=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -u "$GRAFANA_USER:$GRAFANA_PASSWORD" \
            -d "$DATASOURCE_PAYLOAD" \
            "$GRAFANA_URL/api/datasources")
        
        if echo "$DATASOURCE_RESPONSE" | jq -e '.id' > /dev/null 2>&1; then
            print_success "Prometheus datasource created successfully"
        else
            print_error "Failed to create Prometheus datasource"
            print_error "Response: $DATASOURCE_RESPONSE"
            exit 1
        fi
    else
        print_success "Prometheus datasource already exists"
    fi
}

# Main execution
main() {
    print_header "New Appointment Metrics Dashboard Import"
    
    check_grafana
    check_dashboard_file
    configure_datasource
    import_dashboard
    
    echo ""
    print_success "🎉 New dashboard import completed successfully!"
    echo ""
    print_info "📊 Access your new dashboard at: $GRAFANA_URL/d/appointment-metrics"
    print_info "🔑 Login credentials: admin/admin"
    echo ""
    print_warning "⚠️  IMPORTANT: You need to restart your application to see the new metrics!"
    print_info "1. Stop your current application (Ctrl+C)"
    print_info "2. Restart with: ./gradlew bootRun"
    print_info "3. Create some appointments to generate data"
    echo ""
    print_info "📈 The new dashboard shows:"
    print_info "  - Total Scheduled Appointments Successfully"
    print_info "  - Total Scheduling Failures (conflicts + errors)"
    print_info "  - Appointments Completed Successfully"
    print_info "  - Appointments Rescheduled"
    print_info "  - Method execution times for scheduling process"
    print_info "  - Time series charts showing trends"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_info "On macOS: brew install jq"
    print_info "On Ubuntu: sudo apt-get install jq"
    exit 1
fi

main "$@"
