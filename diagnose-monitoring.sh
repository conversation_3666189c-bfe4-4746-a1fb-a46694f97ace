#!/bin/bash

# Script to diagnose monitoring issues

set -e

# Configuration
API_URL="http://localhost:8081/api"
PROMETHEUS_URL="http://localhost:9090"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check application status
check_application() {
    print_header "APPLICATION STATUS"
    
    if curl -s "$API_URL/actuator/health" > /dev/null 2>&1; then
        print_success "Application is running"
        
        # Check application info
        local app_info=$(curl -s "$API_URL/actuator/info" 2>/dev/null || echo "{}")
        echo "Application details:"
        echo "$app_info" | jq '.' 2>/dev/null || echo "No application info available"
    else
        print_error "Application is not running"
        print_info "Please start your application with: ./gradlew bootRun"
        return 1
    fi
}

# Check current metrics
check_current_metrics() {
    print_header "CURRENT METRICS STATUS"
    
    # Check appointment operations
    local appointment_ops=$(curl -s "$API_URL/actuator/prometheus" | grep "appointment_operations_total" | wc -l)
    print_info "appointment_operations_total metrics: $appointment_ops lines"
    
    if [ "$appointment_ops" -gt 0 ]; then
        print_info "Sample appointment operations:"
        curl -s "$API_URL/actuator/prometheus" | grep "appointment_operations_total" | head -3
        echo ""
    fi
    
    # Check enhanced salon metrics
    local salon_metrics=$(curl -s "$API_URL/actuator/prometheus" | grep "salon_appointments" | wc -l)
    print_info "Enhanced salon metrics: $salon_metrics lines"
    
    if [ "$salon_metrics" -gt 0 ]; then
        print_success "Enhanced monitoring code is active"
        print_info "Sample salon metrics:"
        curl -s "$API_URL/actuator/prometheus" | grep "salon_appointments" | head -3
        echo ""
    else
        print_warning "Enhanced monitoring code not detected"
        print_warning "You may need to restart your application to pick up the new monitoring code"
    fi
    
    # Check step metrics
    local step_metrics=$(curl -s "$API_URL/actuator/prometheus" | grep "appointment_step" | wc -l)
    print_info "Step-level metrics: $step_metrics lines"
    
    if [ "$step_metrics" -gt 0 ]; then
        print_success "Step-level monitoring is working"
    else
        print_warning "No step-level metrics found"
    fi
}

# Check Prometheus scraping
check_prometheus_scraping() {
    print_header "PROMETHEUS SCRAPING STATUS"
    
    # Check if Prometheus is scraping
    local targets=$(curl -s "$PROMETHEUS_URL/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job=="animalia-backend") | .health')
    
    if [ "$targets" = "up" ]; then
        print_success "Prometheus is successfully scraping the application"
        
        # Check last scrape time
        local last_scrape=$(curl -s "$PROMETHEUS_URL/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job=="animalia-backend") | .lastScrape')
        print_info "Last scrape: $last_scrape"
        
        # Check scrape duration
        local scrape_duration=$(curl -s "$PROMETHEUS_URL/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job=="animalia-backend") | .scrapePool')
        print_info "Scrape pool: $scrape_duration"
    else
        print_error "Prometheus is not scraping the application properly"
        print_info "Target status: $targets"
        return 1
    fi
}

# Check data in Prometheus
check_prometheus_data() {
    print_header "PROMETHEUS DATA STATUS"
    
    # Check appointment operations in Prometheus
    local prom_data=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_operations_total" | jq -r '.data.result | length')
    print_info "appointment_operations_total series in Prometheus: $prom_data"
    
    if [ "$prom_data" -gt 0 ]; then
        print_success "Prometheus has appointment data"
        
        # Show latest data
        print_info "Latest appointment data in Prometheus:"
        curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_operations_total" | jq -r '.data.result[] | "Salon: \(.metric.salon_id // "unknown") | Success: \(.metric.success) | Value: \(.value[1]) | Time: \(.value[0])"'
        echo ""
    else
        print_warning "No appointment data found in Prometheus"
    fi
    
    # Check enhanced metrics in Prometheus
    local salon_prom_data=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=salon_appointments_total" | jq -r '.data.result | length')
    print_info "salon_appointments_total series in Prometheus: $salon_prom_data"
    
    if [ "$salon_prom_data" -gt 0 ]; then
        print_success "Enhanced salon metrics are in Prometheus"
    else
        print_warning "Enhanced salon metrics not found in Prometheus"
    fi
}

# Test appointment creation
test_appointment_creation() {
    print_header "TESTING APPOINTMENT CREATION"
    
    print_info "Creating a test appointment to check if metrics update..."
    
    local test_response=$(curl -s -X POST "$API_URL/appointments" \
        -H "Content-Type: application/json" \
        -d '{
            "salonId": "1df67c0b-de33-4a47-9770-941e6c181291",
            "staffId": "3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d",
            "appointmentDate": "2024-12-30",
            "startTime": "14:00",
            "endTime": "15:00",
            "clientName": "Test Client Monitoring",
            "clientPhone": "+40123456999",
            "petName": "Test Pet Monitoring",
            "petBreed": "Test Breed",
            "serviceIds": ["service-1"],
            "isNewClient": true,
            "isNewPet": true,
            "notes": "Test appointment for monitoring diagnosis"
        }' 2>/dev/null || echo "error")
    
    if echo "$test_response" | grep -q "error\|Error\|exception\|conflict" 2>/dev/null; then
        print_warning "Test appointment may have failed (this could be expected due to conflicts)"
        print_info "Response: $test_response"
    else
        print_success "Test appointment created successfully"
    fi
    
    # Wait a moment for metrics to update
    sleep 2
    
    # Check if metrics updated
    print_info "Checking if metrics updated after test appointment..."
    local new_count=$(curl -s "$API_URL/actuator/prometheus" | grep "appointment_operations_total" | wc -l)
    print_info "Current appointment_operations_total metrics: $new_count lines"
}

# Provide recommendations
provide_recommendations() {
    print_header "RECOMMENDATIONS"
    
    print_info "Based on the diagnosis, here are the recommended actions:"
    echo ""
    
    # Check if enhanced monitoring is active
    local salon_metrics=$(curl -s "$API_URL/actuator/prometheus" | grep "salon_appointments" | wc -l)
    
    if [ "$salon_metrics" -eq 0 ]; then
        print_warning "🔄 RESTART REQUIRED: Enhanced monitoring code not detected"
        print_info "1. Stop your application (Ctrl+C if running with ./gradlew bootRun)"
        print_info "2. Restart with: ./gradlew bootRun"
        print_info "3. Wait for application to fully start"
        print_info "4. Try creating appointments again"
        echo ""
    fi
    
    print_info "📊 To see data in Grafana:"
    print_info "1. Make sure you have recent appointment data (create some appointments)"
    print_info "2. Check the time range in Grafana (try 'Last 1 hour' or 'Last 6 hours')"
    print_info "3. Refresh the dashboard"
    print_info "4. Use the test data generator: ./generate-test-data.sh"
    echo ""
    
    print_info "🔍 For further debugging:"
    print_info "- Application metrics: $API_URL/actuator/prometheus"
    print_info "- Prometheus targets: $PROMETHEUS_URL/targets"
    print_info "- Prometheus query: $PROMETHEUS_URL/graph"
    print_info "- Grafana dashboard: http://localhost:3000/d/animalia-appointments"
}

# Main execution
main() {
    print_header "MONITORING DIAGNOSIS"
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    check_application
    check_current_metrics
    check_prometheus_scraping
    check_prometheus_data
    test_appointment_creation
    provide_recommendations
    
    echo ""
    print_success "🎉 Diagnosis completed!"
    echo ""
    print_info "💡 If you still don't see data in Grafana after following the recommendations,"
    print_info "   run this script again to see if the metrics are updating."
}

main "$@"
