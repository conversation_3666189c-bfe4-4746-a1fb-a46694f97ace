#!/bin/bash

# Script to test all appointment metrics for the new dashboard

set -e

# Configuration
API_URL="http://localhost:8081/api"
SALON_ID="1df67c0b-de33-4a47-9770-941e6c181291"
STAFF_ID="3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check if application is running
check_application() {
    print_info "Checking if application is running..."
    if curl -s "$API_URL/actuator/health" > /dev/null 2>&1; then
        print_success "Application is running at $API_URL"
    else
        print_error "Application is not accessible at $API_URL"
        print_info "Please start your application with: ./gradlew bootRun"
        exit 1
    fi
}

# Generate successful appointments
generate_successful_appointments() {
    print_header "GENERATING SUCCESSFUL APPOINTMENTS"
    
    local success_count=0
    local dates=("2024-12-25" "2024-12-26" "2024-12-27")
    local times=("09:00" "10:00" "11:00" "14:00" "15:00")
    local clients=("John Success" "Jane Success" "Bob Success" "Alice Success" "Charlie Success")
    local pets=("Lucky" "Buddy" "Bella" "Max" "Luna")
    
    for i in {1..5}; do
        local date=${dates[$((i % 3))]}
        local start_time=${times[$((i % 5))]}
        local end_hour=$((${start_time:0:2} + 1))
        local end_time=$(printf "%02d:00" $end_hour)
        local client=${clients[$((i - 1))]}
        local pet=${pets[$((i - 1))]}
        local phone="+4012345000$i"
        
        print_info "Creating successful appointment $i: $client with $pet on $date $start_time-$end_time"
        
        local response=$(curl -s -X POST "$API_URL/appointments" \
            -H "Content-Type: application/json" \
            -d "{
                \"salonId\": \"$SALON_ID\",
                \"staffId\": \"$STAFF_ID\",
                \"appointmentDate\": \"$date\",
                \"startTime\": \"$start_time\",
                \"endTime\": \"$end_time\",
                \"clientName\": \"$client\",
                \"clientPhone\": \"$phone\",
                \"petName\": \"$pet\",
                \"petBreed\": \"Golden Retriever\",
                \"serviceIds\": [\"service-1\"],
                \"isNewClient\": true,
                \"isNewPet\": true,
                \"notes\": \"Test successful appointment for metrics\"
            }" 2>/dev/null || echo "error")
        
        if echo "$response" | grep -q "error\|Error\|exception" 2>/dev/null; then
            print_warning "Appointment $i may have failed"
        else
            success_count=$((success_count + 1))
            print_success "Appointment $i created successfully"
        fi
        
        sleep 1
    done
    
    print_success "Generated $success_count successful appointments"
}

# Generate conflicting appointments
generate_conflicting_appointments() {
    print_header "GENERATING CONFLICTING APPOINTMENTS"
    
    local conflict_count=0
    
    # Try to schedule appointments at the same time slots
    for i in {1..3}; do
        local date="2024-12-25"
        local start_time="09:00"
        local end_time="10:00"
        local client="Conflict Client $i"
        local pet="Conflict Pet $i"
        local phone="+4012346000$i"
        
        print_info "Creating conflicting appointment $i: $client with $pet on $date $start_time-$end_time"
        
        local response=$(curl -s -X POST "$API_URL/appointments" \
            -H "Content-Type: application/json" \
            -d "{
                \"salonId\": \"$SALON_ID\",
                \"staffId\": \"$STAFF_ID\",
                \"appointmentDate\": \"$date\",
                \"startTime\": \"$start_time\",
                \"endTime\": \"$end_time\",
                \"clientName\": \"$client\",
                \"clientPhone\": \"$phone\",
                \"petName\": \"$pet\",
                \"petBreed\": \"Mixed Breed\",
                \"serviceIds\": [\"service-1\"],
                \"isNewClient\": true,
                \"isNewPet\": true,
                \"notes\": \"Test conflict appointment\"
            }" 2>/dev/null || echo "conflict_expected")
        
        if echo "$response" | grep -q "conflict\|Conflict\|unavailable" 2>/dev/null; then
            conflict_count=$((conflict_count + 1))
            print_success "Conflict $i generated successfully (expected failure)"
        else
            print_warning "Expected conflict but appointment may have succeeded"
        fi
        
        sleep 1
    done
    
    print_success "Generated $conflict_count conflicts for failure metrics"
}

# Generate internal errors
generate_internal_errors() {
    print_header "GENERATING INTERNAL ERRORS"
    
    local error_count=0
    
    # Invalid salon ID
    print_info "Testing with invalid salon ID..."
    curl -s -X POST "$API_URL/appointments" \
        -H "Content-Type: application/json" \
        -d "{
            \"salonId\": \"invalid-salon-id\",
            \"staffId\": \"$STAFF_ID\",
            \"appointmentDate\": \"2024-12-28\",
            \"startTime\": \"10:00\",
            \"endTime\": \"11:00\",
            \"clientName\": \"Error Test Client\",
            \"clientPhone\": \"+40123456789\",
            \"petName\": \"Error Pet\",
            \"petBreed\": \"Test Breed\",
            \"serviceIds\": [\"service-1\"],
            \"isNewClient\": true,
            \"isNewPet\": true
        }" > /dev/null 2>&1 && error_count=$((error_count + 1))
    
    # Invalid staff ID
    print_info "Testing with invalid staff ID..."
    curl -s -X POST "$API_URL/appointments" \
        -H "Content-Type: application/json" \
        -d "{
            \"salonId\": \"$SALON_ID\",
            \"staffId\": \"invalid-staff-id\",
            \"appointmentDate\": \"2024-12-28\",
            \"startTime\": \"11:00\",
            \"endTime\": \"12:00\",
            \"clientName\": \"Error Test Client 2\",
            \"clientPhone\": \"+40123456790\",
            \"petName\": \"Error Pet 2\",
            \"petBreed\": \"Test Breed\",
            \"serviceIds\": [\"service-1\"],
            \"isNewClient\": true,
            \"isNewPet\": true
        }" > /dev/null 2>&1 && error_count=$((error_count + 1))
    
    print_success "Generated various internal errors for error tracking"
}

# Test completion and rescheduling (if endpoints exist)
test_completion_and_rescheduling() {
    print_header "TESTING COMPLETION AND RESCHEDULING"
    
    print_info "Note: Completion and rescheduling will be tested when appointments are created"
    print_info "The metrics will be recorded when you use the actual completion/rescheduling features"
    print_warning "For now, we're focusing on scheduling metrics which are the main ones"
}

# Check generated metrics
check_generated_metrics() {
    print_header "CHECKING GENERATED METRICS"
    
    print_info "Waiting for metrics to be scraped by Prometheus..."
    sleep 5
    
    # Check for new appointment outcome metrics
    local scheduled_success=$(curl -s "$API_URL/actuator/prometheus" | grep -c "appointment_scheduled_success" || echo "0")
    local scheduled_conflict=$(curl -s "$API_URL/actuator/prometheus" | grep -c "appointment_scheduled_conflict" || echo "0")
    local scheduled_error=$(curl -s "$API_URL/actuator/prometheus" | grep -c "appointment_scheduled_error" || echo "0")
    local method_execution=$(curl -s "$API_URL/actuator/prometheus" | grep -c "appointment_method_execution" || echo "0")
    
    print_info "Metrics generated:"
    print_info "  - Scheduled Success metrics: $scheduled_success lines"
    print_info "  - Scheduled Conflict metrics: $scheduled_conflict lines"
    print_info "  - Scheduled Error metrics: $scheduled_error lines"
    print_info "  - Method Execution metrics: $method_execution lines"
    
    if [ "$scheduled_success" -gt 0 ] || [ "$scheduled_conflict" -gt 0 ] || [ "$scheduled_error" -gt 0 ]; then
        print_success "New appointment metrics are being generated!"
    else
        print_warning "New metrics not found - you may need to restart the application"
    fi
    
    print_info "View all metrics at: $API_URL/actuator/prometheus"
}

# Provide dashboard access info
show_dashboard_info() {
    print_header "DASHBOARD ACCESS"
    
    print_success "🎉 Test data generation completed!"
    echo ""
    print_info "📊 Access your new dashboard at: http://localhost:3000/d/appointment-metrics"
    print_info "🔑 Username: admin"
    print_info "🔑 Password: admin"
    echo ""
    print_info "📈 The dashboard shows:"
    print_info "  ✅ Total Scheduled Appointments Successfully"
    print_info "  ❌ Total Scheduling Failures (conflicts + errors)"
    print_info "  ✅ Appointments Completed Successfully"
    print_info "  🔄 Appointments Rescheduled"
    print_info "  ⏱️  Method execution times"
    print_info "  📊 Time series trends"
    echo ""
    print_info "🔄 If you don't see data:"
    print_info "  1. Make sure you restarted the application with the new monitoring code"
    print_info "  2. Check the time range in Grafana (try 'Last 1 hour')"
    print_info "  3. Refresh the dashboard"
    print_info "  4. Run this script again to generate more data"
}

# Main execution
main() {
    print_header "Appointment Metrics Test Data Generator"
    
    check_application
    generate_successful_appointments
    generate_conflicting_appointments
    generate_internal_errors
    test_completion_and_rescheduling
    check_generated_metrics
    show_dashboard_info
}

main "$@"
