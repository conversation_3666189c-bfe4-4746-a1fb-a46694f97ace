# Animalia Appointment Monitoring Setup

## 🚀 Enhanced Monitoring Features

The enhanced monitoring system now tracks detailed metrics for appointment scheduling with salon-level granularity:

### 📊 Metrics Tracked

1. **Total Appointments by Salon**
   - Success count per salon
   - Failure count per salon
   - Success/failure ratio

2. **Appointment Duration Tracking**
   - Average time to complete appointments by salon
   - Duration percentiles (50th, 95th)

3. **Detailed Error and Exception Tracking**
   - Exception types and frequencies
   - Salon-specific error rates
   - Context-aware exception logging

4. **Step-Level Performance Monitoring**
   - Average duration for each step in the appointment scheduling flow:
     - `validate_request` - Request validation time
     - `create_appointment` - Appointment creation time
     - `save_appointment` - Database save time
     - `handle_recurring` - Recurring appointment processing
     - `publish_events` - Event publishing time

### 🔧 Setup Instructions

#### 1. Start Your Application
```bash
./gradlew bootRun
```

#### 2. Start Monitoring Stack
```bash
# Using docker-compose
docker-compose -f docker-compose.monitoring.yml up -d

# Or using the setup script
./setup-monitoring.sh
```

#### 3. Import the Dashboard
```bash
# Make sure jq is installed (for JSON processing)
# macOS: brew install jq
# Ubuntu: sudo apt-get install jq

# Import the dashboard
./import-dashboard.sh
```

#### 4. Access Grafana
- URL: http://localhost:3000
- Username: `admin`
- Password: `admin`
- Dashboard: "Animalia Appointment Monitoring Dashboard"

### 📈 Dashboard Panels

1. **Total Appointments by Salon - Success vs Failures**
   - Shows absolute numbers of successful and failed appointments per salon
   - Helps identify which salons have the most activity and issues

2. **Appointment Success Rate by Salon (%)**
   - Gauge showing success percentage per salon
   - Color-coded: Red (<70%), Yellow (70-90%), Green (>90%)

3. **Average Appointment Duration by Salon**
   - Time series showing how long appointments take to complete
   - Helps identify performance bottlenecks by salon

4. **Errors and Exceptions by Type**
   - Pie chart showing distribution of different exception types
   - Helps prioritize which errors to fix first

5. **Exception Rate by Salon**
   - Time series showing error rates over time per salon
   - Helps identify problematic periods or salons

6. **Average Step Duration in Schedule Appointment Flow**
   - Table showing average time for each step in the scheduling process
   - Helps identify which steps are slowest and need optimization

7. **Step Failure Rate by Salon**
   - Shows which steps fail most often in each salon
   - Helps pinpoint specific issues in the workflow

8. **Appointment Conflicts and Alternatives**
   - Tracks how often conflicts occur and alternatives are generated
   - Helps understand scheduling efficiency

9. **Workflow Execution Times (Percentiles)**
   - Shows 50th and 95th percentile execution times
   - Helps understand performance distribution

### 🧪 Generating Test Data

To see the dashboard in action, generate some test data:

#### Successful Appointments
```bash
curl -X POST http://localhost:8081/api/appointments \
  -H "Content-Type: application/json" \
  -d '{
    "salonId": "1df67c0b-de33-4a47-9770-941e6c181291",
    "staffId": "3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d",
    "appointmentDate": "2024-12-25",
    "startTime": "10:00",
    "endTime": "11:00",
    "clientName": "Test Client",
    "clientPhone": "+40123456789",
    "petName": "Test Pet",
    "petBreed": "Golden Retriever",
    "serviceIds": ["service-id-1"],
    "isNewClient": true,
    "isNewPet": true
  }'
```

#### Conflicting Appointments (to generate failures)
```bash
# Schedule the same time slot again to create a conflict
curl -X POST http://localhost:8081/api/appointments \
  -H "Content-Type: application/json" \
  -d '{
    "salonId": "1df67c0b-de33-4a47-9770-941e6c181291",
    "staffId": "3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d",
    "appointmentDate": "2024-12-25",
    "startTime": "10:00",
    "endTime": "11:00",
    "clientName": "Another Client",
    "clientPhone": "+40123456790",
    "petName": "Another Pet",
    "petBreed": "Labrador",
    "serviceIds": ["service-id-1"],
    "isNewClient": true,
    "isNewPet": true
  }'
```

### 🔍 Key Metrics to Monitor

1. **Success Rate**: Should be >90% for healthy operations
2. **Average Duration**: Monitor for increases that might indicate performance issues
3. **Exception Rate**: Should be low and stable
4. **Step Duration**: Identify bottlenecks in the scheduling flow
5. **Conflict Rate**: High conflicts might indicate scheduling logic issues

### 🚨 Alerting (Optional)

You can set up alerts in Grafana for:
- Success rate drops below 90%
- Average appointment duration exceeds 5 seconds
- Exception rate exceeds 1 per minute
- Any step duration exceeds 2 seconds

### 🛠️ Troubleshooting

#### Dashboard Not Showing Data
1. Check if your application is running and accessible
2. Verify Prometheus is scraping metrics: http://localhost:9090/targets
3. Check if metrics are being generated: http://localhost:8081/api/actuator/prometheus
4. Generate some test appointments to create data

#### Metrics Missing
1. Ensure the enhanced monitoring code is deployed
2. Check application logs for any monitoring-related errors
3. Verify the metric names match those in the dashboard queries

#### Performance Impact
The enhanced monitoring adds minimal overhead:
- ~1-2ms per appointment operation
- Metrics are recorded asynchronously
- No impact on business logic flow

### 📝 Customization

You can customize the dashboard by:
1. Editing the JSON file and re-importing
2. Adding new panels through Grafana UI
3. Creating additional dashboards for specific use cases
4. Setting up custom alerts and notifications

The monitoring system is designed to be extensible - you can easily add new metrics by using the `WorkflowMetrics` class methods in your code.
