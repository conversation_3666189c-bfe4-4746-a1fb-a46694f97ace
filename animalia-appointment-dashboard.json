{"dashboard": {"id": null, "title": "Animalia Appointment Monitoring Dashboard", "tags": ["animalia", "appointments"], "timezone": "", "panels": [{"id": 1, "title": "Total Appointments by Salon - Success vs Failures", "type": "stat", "targets": [{"expr": "sum by (salon_id) (increase(appointment_operations_total{success=\"true\"}[$__range]))", "legendFormat": "Success - Salon {{salon_id}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "sum by (salon_id) (increase(appointment_operations_total{success=\"false\"}[$__range]))", "legendFormat": "Failures - Salon {{salon_id}}", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Appointment Success Rate by Salon (%)", "type": "gauge", "targets": [{"expr": "clamp_max(clamp_min((sum by (salon_id) (rate(appointment_operations_total{success=\"true\"}[$__rate_interval])) / (sum by (salon_id) (rate(appointment_operations_total[$__rate_interval])) + 0.0001) * 100), 0), 100) or vector(0)", "legendFormat": "Salon {{salon_id}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"null": {"text": "No Data"}}, "type": "special"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100, "noValue": "No Data"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Average Appointment Duration by Salon", "type": "timeseries", "targets": [{"expr": "(avg by (salon_id) (rate(appointment_schedule_seconds_sum[$__rate_interval]) / (rate(appointment_schedule_seconds_count[$__rate_interval]) + 0.0001)) * 1000) and (rate(appointment_schedule_seconds_count[$__rate_interval]) > 0)", "legendFormat": "Salon {{salon_id}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"null": {"text": "No Data"}}, "type": "special"}], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms", "noValue": "No Data"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Errors and Exceptions by Type", "type": "piechart", "targets": [{"expr": "sum by (error_type) (increase(appointment_operations_total{success=\"false\"}[$__range]))", "legendFormat": "{{error_type}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Exception Rate by Salon", "type": "timeseries", "targets": [{"expr": "rate(appointment_operations_total{success=\"false\"}[$__rate_interval])", "legendFormat": "Salon {{salon_id}} - {{error_type}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"null": {"text": "No Data"}}, "type": "special"}], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Average Step Duration in Schedule Appointment Flow", "type": "table", "targets": [{"expr": "(avg by (step, salon_id) (rate(appointment_step_duration_seconds_sum{workflow=\"schedule_appointment\"}[$__rate_interval]) / (rate(appointment_step_duration_seconds_count{workflow=\"schedule_appointment\"}[$__rate_interval]) + 0.0001)) * 1000) and (rate(appointment_step_duration_seconds_count{workflow=\"schedule_appointment\"}[$__rate_interval]) > 0)", "legendFormat": "", "refId": "A", "format": "table", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [{"options": {"null": {"text": "No Data"}}, "type": "special"}], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 5000}]}, "unit": "ms", "noValue": "No Data"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"salon_id": "Salon ID", "step": "Step Name", "Value": "Average Duration (ms)"}}}]}, {"id": 7, "title": "Step Failure Rate by Salon", "type": "timeseries", "targets": [{"expr": "rate(appointment_step_executions_total{success=\"false\"}[$__rate_interval])", "legendFormat": "{{salon_id}} - {{step}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 8, "title": "Appointment Conflicts and Alternatives", "type": "timeseries", "targets": [{"expr": "rate(appointment_conflicts_detected[$__rate_interval])", "legendFormat": "Conflicts - Salon {{salon_id}}", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "rate(appointment_alternatives_generated[$__rate_interval])", "legendFormat": "Alternatives - Salon {{salon_id}}", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 9, "title": "Workflow Execution Times (Percentiles)", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(workflow_schedule_appointment_seconds_bucket[$__rate_interval])) * 1000", "legendFormat": "95th percentile - Schedule Appointment", "refId": "A", "datasource": {"type": "prometheus", "uid": "prometheus"}}, {"expr": "histogram_quantile(0.50, rate(workflow_schedule_appointment_seconds_bucket[$__rate_interval])) * 1000", "legendFormat": "50th percentile - Schedule Appointment", "refId": "B", "datasource": {"type": "prometheus", "uid": "prometheus"}}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}], "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "refresh": "10s", "schemaVersion": 37, "style": "dark", "uid": "animalia-appointments", "version": 1, "weekStart": ""}}