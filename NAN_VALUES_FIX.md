# NaN Values Fix in Grafana Dashboard

## ✅ **Fixed: NaN (Not a Number) Values**

### **🔍 Root Causes of NaN Values**

NaN values in Grafana typically occur due to:

1. **Division by Zero**: When the denominator in a calculation is 0
2. **Missing Data**: When metrics don't exist for certain time periods
3. **Rate Calculations on Empty Data**: When `rate()` functions have no data to work with
4. **Histogram Quantiles on Empty Buckets**: When percentile calculations have no data

### **🛠️ Fixes Applied**

#### **1. Success Rate Calculation**
**Before (❌ Caused NaN):**
```promql
sum by (salon_id) (rate(appointment_operations_total{success="true"}[$__rate_interval])) / (sum by (salon_id) (rate(appointment_operations_total[$__rate_interval]))) * 100
```

**After (✅ NaN-Safe):**
```promql
clamp_max(clamp_min((sum by (salon_id) (rate(appointment_operations_total{success="true"}[$__rate_interval])) / (sum by (salon_id) (rate(appointment_operations_total[$__rate_interval])) + 0.0001) * 100), 0), 100) or vector(0)
```

**What this does:**
- **`+ 0.0001`**: Prevents division by zero by adding a tiny value
- **`clamp_min(..., 0)`**: Ensures result is never below 0%
- **`clamp_max(..., 100)`**: Ensures result is never above 100%
- **`or vector(0)`**: Returns 0 if no data exists

#### **2. Average Duration Calculation**
**Before (❌ Caused NaN):**
```promql
avg by (salon_id) (rate(appointment_schedule_seconds_sum[$__rate_interval]) / rate(appointment_schedule_seconds_count[$__rate_interval])) * 1000
```

**After (✅ NaN-Safe):**
```promql
(avg by (salon_id) (rate(appointment_schedule_seconds_sum[$__rate_interval]) / (rate(appointment_schedule_seconds_count[$__rate_interval]) + 0.0001)) * 1000) and (rate(appointment_schedule_seconds_count[$__rate_interval]) > 0)
```

**What this does:**
- **`+ 0.0001`**: Prevents division by zero
- **`and (...> 0)`**: Only shows results when there's actual data
- **No result shown** when no data exists (instead of NaN)

#### **3. Step Duration Calculation**
**Before (❌ Caused NaN):**
```promql
avg by (step, salon_id) (rate(appointment_step_duration_seconds_sum{workflow="schedule_appointment"}[$__rate_interval]) / rate(appointment_step_duration_seconds_count{workflow="schedule_appointment"}[$__rate_interval])) * 1000
```

**After (✅ NaN-Safe):**
```promql
(avg by (step, salon_id) (rate(appointment_step_duration_seconds_sum{workflow="schedule_appointment"}[$__rate_interval]) / (rate(appointment_step_duration_seconds_count{workflow="schedule_appointment"}[$__rate_interval]) + 0.0001)) * 1000) and (rate(appointment_step_duration_seconds_count{workflow="schedule_appointment"}[$__rate_interval]) > 0)
```

#### **4. Visualization Settings**
Added proper null handling in Grafana field configurations:

**Null Value Mappings:**
```json
"mappings": [
  {
    "options": {
      "null": {
        "text": "No Data"
      }
    },
    "type": "special"
  }
]
```

**No Value Display:**
```json
"noValue": "No Data"
```

**Span Nulls in Time Series:**
```json
"spanNulls": true
```

### **🎯 Prometheus Functions Used**

#### **`clamp_min(value, min)`**
- Ensures the result is never below the minimum value
- Example: `clamp_min(result, 0)` ensures no negative percentages

#### **`clamp_max(value, max)`**
- Ensures the result is never above the maximum value
- Example: `clamp_max(result, 100)` ensures percentages don't exceed 100%

#### **`vector(value)`**
- Creates a constant vector with the specified value
- Example: `vector(0)` returns 0 when no data exists

#### **`or` Operator**
- Returns the right side if the left side has no data
- Example: `query or vector(0)` returns 0 if query has no results

#### **`and` Operator**
- Only returns results when both sides are true/have data
- Example: `query and (count > 0)` only shows results when count exists

### **📊 Panel-Specific Improvements**

#### **1. Success Rate Gauge**
- **Range**: Properly clamped between 0-100%
- **No Data**: Shows "No Data" instead of NaN
- **Color Coding**: Red (<70%), Yellow (70-90%), Green (>90%)

#### **2. Duration Time Series**
- **Span Nulls**: Connects data points across gaps
- **No Data**: Hides lines when no data exists
- **Units**: Properly shows milliseconds

#### **3. Step Duration Table**
- **No Data**: Shows "No Data" in cells instead of NaN
- **Sorting**: Works properly without NaN interference
- **Color Coding**: Thresholds work correctly

#### **4. Error Rate Charts**
- **Zero Handling**: Shows 0 instead of NaN when no errors
- **Time Series**: Smooth lines without NaN gaps

### **🔍 Testing the Fixes**

To verify NaN values are gone:

1. **Open Dashboard**: http://localhost:3000/d/animalia-appointments
2. **Check Success Rate**: Should show 0% or actual percentage, never NaN
3. **Check Duration Charts**: Should show smooth lines or "No Data"
4. **Check Tables**: Should show "No Data" in empty cells
5. **Change Time Range**: Try different ranges to test edge cases

### **📈 Expected Behavior Now**

#### **When Data Exists:**
- **Success Rate**: Shows actual percentage (0-100%)
- **Duration**: Shows actual timing in milliseconds
- **Counts**: Shows actual numbers
- **Charts**: Display smooth, connected lines

#### **When No Data Exists:**
- **Gauges**: Show "No Data" text
- **Time Series**: Show empty charts (no lines)
- **Tables**: Show "No Data" in cells
- **Pie Charts**: Show empty or "No data" message

#### **When Partial Data Exists:**
- **Success Rate**: Calculates correctly with available data
- **Duration**: Shows averages for periods with data
- **Time Series**: Shows lines only where data exists
- **Gaps**: Handled gracefully without NaN

### **🚀 Benefits of the Fix**

1. **Clean Visualizations**: No more ugly NaN values cluttering charts
2. **Proper Calculations**: Mathematical operations work correctly
3. **Better UX**: Users see "No Data" instead of confusing NaN
4. **Reliable Alerts**: Alert conditions work properly without NaN interference
5. **Export Friendly**: Data exports don't contain NaN values

### **🔧 Technical Details**

#### **Division by Zero Prevention**
- Added small epsilon values (0.0001) to denominators
- Ensures division never results in infinity or NaN
- Small enough to not affect actual calculations

#### **Conditional Display**
- Use `and` operators to only show results when data exists
- Prevents showing calculated values based on empty data
- Maintains data integrity

#### **Fallback Values**
- Use `or vector(0)` to provide sensible defaults
- Ensures panels always have some value to display
- Prevents completely empty visualizations

The dashboard now provides a much cleaner, more professional experience without any NaN values! 🎉
