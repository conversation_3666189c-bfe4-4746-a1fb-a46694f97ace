package ro.animaliaprogramari.animalia.application.monitoring

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.MonitoringService
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Utility class for tracking workflow and method execution metrics
 * Provides convenient methods for monitoring business operations
 */
@Component
class WorkflowMetrics(
    private val monitoringService: MonitoringService
) {

    private val logger = LoggerFactory.getLogger(WorkflowMetrics::class.java)

    /**
     * Execute a workflow with comprehensive monitoring
     */
    fun <T> executeWorkflow(
        workflowName: String,
        tags: Map<String, String> = emptyMap(),
        operation: () -> T
    ): T {
        val timer = monitoringService.startTimer("workflow.$workflowName", tags)
        val startTime = System.currentTimeMillis()
        
        return try {
            logger.info("Starting workflow: $workflowName")
            val result = operation()
            val duration = System.currentTimeMillis() - startTime
            
            timer.stop(success = true)
            monitoringService.recordWorkflowExecution(
                workflowName = workflowName,
                success = true,
                durationMs = duration,
                tags = tags
            )
            
            logger.info("Workflow completed successfully: $workflowName (${duration}ms)")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorType = e.javaClass.simpleName
            
            timer.stop(success = false, errorType = errorType)
            monitoringService.recordWorkflowExecution(
                workflowName = workflowName,
                success = false,
                durationMs = duration,
                errorType = errorType,
                tags = tags
            )
            
            logger.error("Workflow failed: $workflowName (${duration}ms) - $errorType: ${e.message}")
            throw e
        }
    }
    
    /**
     * Execute a method with monitoring
     */
    fun <T> executeMethod(
        methodName: String,
        className: String,
        tags: Map<String, String> = emptyMap(),
        operation: () -> T
    ): T {
        val timer = monitoringService.startTimer("method.$className.$methodName", tags)
        val startTime = System.currentTimeMillis()
        
        return try {
            logger.debug("Starting method: $className.$methodName")
            val result = operation()
            val duration = System.currentTimeMillis() - startTime
            
            timer.stop(success = true)
            monitoringService.recordMethodExecution(
                methodName = methodName,
                className = className,
                success = true,
                durationMs = duration,
                tags = tags
            )
            
            logger.debug("Method completed: $className.$methodName (${duration}ms)")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorType = e.javaClass.simpleName
            
            timer.stop(success = false, errorType = errorType)
            monitoringService.recordMethodExecution(
                methodName = methodName,
                className = className,
                success = false,
                durationMs = duration,
                errorType = errorType,
                tags = tags
            )
            
            logger.debug("Method failed: $className.$methodName (${duration}ms) - $errorType: ${e.message}")
            throw e
        }
    }
    
    /**
     * Record appointment workflow specific metrics with enhanced salon-level tracking
     */
    fun recordAppointmentWorkflowMetrics(
        operation: String,
        salonId: String,
        staffId: String,
        success: Boolean,
        durationMs: Long,
        conflictsDetected: Int = 0,
        alternativesGenerated: Int = 0,
        errorType: String? = null
    ) {
        val currentTime = LocalDateTime.now()
        val tags = mapOf(
            "operation" to operation,
            "salon_id" to salonId,
            "staff_id" to staffId,
            "success" to success.toString(),
            "hour" to currentTime.format(DateTimeFormatter.ofPattern("HH")),
            "day_of_week" to currentTime.dayOfWeek.toString(),
            "date" to currentTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        ).let { baseTags ->
            if (errorType != null) baseTags + ("error_type" to errorType) else baseTags
        }

        // Record basic metrics
        monitoringService.recordTiming("appointment.$operation", durationMs, tags)
        monitoringService.incrementCounter("appointment.operations", tags)

        // Enhanced salon-level metrics
        val salonTags = mapOf(
            "salon_id" to salonId,
            "operation" to operation,
            "success" to success.toString(),
            "date" to currentTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            "hour" to currentTime.format(DateTimeFormatter.ofPattern("HH"))
        ).let { baseTags ->
            if (errorType != null) baseTags + ("error_type" to errorType) else baseTags
        }

        // Salon-specific counters for total appointments
        monitoringService.incrementCounter("salon.appointments.total", salonTags)

        // Salon-specific success/failure counters
        if (success) {
            monitoringService.incrementCounter("salon.appointments.success", salonTags)
        } else {
            monitoringService.incrementCounter("salon.appointments.failure", salonTags)
            // Record specific error metrics by salon
            if (errorType != null) {
                monitoringService.incrementCounter("salon.appointments.errors", salonTags)
                monitoringService.recordMetric("salon.error.occurrence", 1.0, salonTags)
            }
        }

        // Salon-specific timing metrics
        monitoringService.recordTiming("salon.appointment.duration", durationMs, salonTags)

        // Record conflict-specific metrics
        if (conflictsDetected > 0) {
            monitoringService.recordMetric("appointment.conflicts_detected", conflictsDetected.toDouble(), tags)
            monitoringService.recordMetric("salon.conflicts.detected", conflictsDetected.toDouble(), salonTags)
        }

        if (alternativesGenerated > 0) {
            monitoringService.recordMetric("appointment.alternatives_generated", alternativesGenerated.toDouble(), tags)
            monitoringService.recordMetric("salon.alternatives.generated", alternativesGenerated.toDouble(), salonTags)
        }

        // Record success rate (both general and salon-specific)
        val successValue = if (success) 1.0 else 0.0
        monitoringService.recordMetric("appointment.success_rate", successValue, tags)
        monitoringService.recordMetric("salon.success_rate", successValue, salonTags)

        logger.debug("Recorded appointment metrics: operation=$operation, salon=$salonId, success=$success, duration=${durationMs}ms")
    }
    
    /**
     * Record business operation metrics
     */
    fun recordBusinessOperation(
        operationType: String,
        entityType: String,
        success: Boolean,
        durationMs: Long,
        additionalMetrics: Map<String, Double> = emptyMap(),
        tags: Map<String, String> = emptyMap()
    ) {
        val operationTags = tags + mapOf(
            "operation_type" to operationType,
            "entity_type" to entityType,
            "success" to success.toString()
        )
        
        // Record timing and counter
        monitoringService.recordTiming("business.operation", durationMs, operationTags)
        monitoringService.incrementCounter("business.operations", operationTags)
        
        // Record additional metrics
        additionalMetrics.forEach { (metricName, value) ->
            monitoringService.recordMetric("business.$metricName", value, operationTags)
        }
        
        // Record success rate
        val successValue = if (success) 1.0 else 0.0
        monitoringService.recordMetric("business.success_rate", successValue, operationTags)
    }
    
    /**
     * Record performance threshold violations
     */
    fun recordPerformanceThreshold(
        operationName: String,
        actualDurationMs: Long,
        thresholdMs: Long,
        tags: Map<String, String> = emptyMap()
    ) {
        if (actualDurationMs > thresholdMs) {
            val violationTags = tags + mapOf(
                "operation" to operationName,
                "threshold_ms" to thresholdMs.toString(),
                "actual_ms" to actualDurationMs.toString(),
                "violation_ratio" to (actualDurationMs.toDouble() / thresholdMs).toString()
            )

            monitoringService.incrementCounter("performance.threshold_violations", violationTags)
            monitoringService.recordMetric("performance.violation_ratio",
                actualDurationMs.toDouble() / thresholdMs, violationTags)

            logger.warn("Performance threshold violation: $operationName took ${actualDurationMs}ms (threshold: ${thresholdMs}ms)")
        }
    }

    /**
     * Enhanced method execution tracking with step-level metrics for appointment scheduling
     */
    fun <T> executeAppointmentStep(
        stepName: String,
        salonId: String,
        workflowName: String = "schedule_appointment",
        tags: Map<String, String> = emptyMap(),
        operation: () -> T
    ): T {
        val enhancedTags = tags + mapOf(
            "step" to stepName,
            "salon_id" to salonId,
            "workflow" to workflowName
        )

        val timer = monitoringService.startTimer("appointment.step.$stepName", enhancedTags)
        val startTime = System.currentTimeMillis()

        return try {
            logger.debug("Starting appointment step: $stepName for salon: $salonId")
            val result = operation()
            val duration = System.currentTimeMillis() - startTime

            timer.stop(success = true)

            // Record step-specific metrics
            monitoringService.recordTiming("appointment.step.duration", duration, enhancedTags)
            monitoringService.incrementCounter("appointment.step.executions", enhancedTags + ("success" to "true"))

            // Record salon-specific step metrics
            val salonStepTags = mapOf(
                "salon_id" to salonId,
                "step" to stepName,
                "success" to "true"
            )
            monitoringService.recordTiming("salon.step.duration", duration, salonStepTags)
            monitoringService.incrementCounter("salon.step.executions", salonStepTags)

            logger.debug("Appointment step completed: $stepName (${duration}ms)")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            val errorType = e.javaClass.simpleName

            timer.stop(success = false, errorType = errorType)

            val failureTags = enhancedTags + mapOf(
                "success" to "false",
                "error_type" to errorType
            )

            // Record step failure metrics
            monitoringService.recordTiming("appointment.step.duration", duration, failureTags)
            monitoringService.incrementCounter("appointment.step.executions", failureTags)
            monitoringService.incrementCounter("appointment.step.failures", failureTags)

            // Record salon-specific step failure metrics
            val salonStepTags = mapOf(
                "salon_id" to salonId,
                "step" to stepName,
                "success" to "false",
                "error_type" to errorType
            )
            monitoringService.recordTiming("salon.step.duration", duration, salonStepTags)
            monitoringService.incrementCounter("salon.step.executions", salonStepTags)
            monitoringService.incrementCounter("salon.step.failures", salonStepTags)

            logger.error("Appointment step failed: $stepName (${duration}ms) - $errorType: ${e.message}")
            throw e
        }
    }

    /**
     * Record detailed exception information for better error tracking
     */
    fun recordDetailedException(
        exception: Exception,
        context: String,
        salonId: String? = null,
        additionalTags: Map<String, String> = emptyMap()
    ) {
        val exceptionTags = mutableMapOf(
            "exception_type" to exception.javaClass.simpleName,
            "context" to context,
            "has_cause" to (exception.cause != null).toString()
        )

        salonId?.let { exceptionTags["salon_id"] = it }
        exceptionTags.putAll(additionalTags)

        // Record exception occurrence
        monitoringService.incrementCounter("exceptions.total", exceptionTags)

        // Record exception by type
        monitoringService.incrementCounter("exceptions.by_type", exceptionTags)

        // Record salon-specific exceptions if salon is provided
        if (salonId != null) {
            val salonExceptionTags = mapOf(
                "salon_id" to salonId,
                "exception_type" to exception.javaClass.simpleName,
                "context" to context
            )
            monitoringService.incrementCounter("salon.exceptions.total", salonExceptionTags)
        }

        // Record exception severity based on type
        val severity = when (exception) {
            is IllegalArgumentException, is IllegalStateException -> "low"
            is SecurityException -> "medium"
            is RuntimeException -> "high"
            else -> "critical"
        }

        monitoringService.incrementCounter("exceptions.by_severity",
            exceptionTags + ("severity" to severity))

        logger.debug("Recorded exception metrics: ${exception.javaClass.simpleName} in context: $context")
    }

    /**
     * Record specific appointment outcomes for dashboard metrics
     */
    fun recordAppointmentOutcome(
        outcome: String,
        salonId: String,
        staffId: String,
        appointmentDate: LocalDate,
        additionalTags: Map<String, String> = emptyMap()
    ) {
        val currentTime = LocalDateTime.now()
        val baseTags = mapOf(
            "outcome" to outcome,
            "salon_id" to salonId,
            "staff_id" to staffId,
            "appointment_date" to appointmentDate.toString(),
            "recorded_date" to currentTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            "recorded_hour" to currentTime.format(DateTimeFormatter.ofPattern("HH")),
            "day_of_week" to appointmentDate.dayOfWeek.toString()
        ) + additionalTags

        // Record the specific outcome counter
        monitoringService.incrementCounter("appointment.outcomes", baseTags)

        // Record outcome by type for easier querying
        when (outcome) {
            "scheduled_successfully" -> {
                monitoringService.incrementCounter("appointment.scheduled.success", baseTags)
            }
            "scheduling_conflict" -> {
                monitoringService.incrementCounter("appointment.scheduled.conflict", baseTags)
            }
            "scheduling_internal_error" -> {
                monitoringService.incrementCounter("appointment.scheduled.error", baseTags)
            }
            "completed_successfully" -> {
                monitoringService.incrementCounter("appointment.completed.success", baseTags)
            }
            "rescheduled_successfully" -> {
                monitoringService.incrementCounter("appointment.rescheduled.success", baseTags)
            }
        }

        logger.debug("Recorded appointment outcome: $outcome for salon: $salonId on date: $appointmentDate")
    }

    /**
     * Record method execution time for specific appointment scheduling steps
     */
    fun recordMethodExecutionTime(
        methodName: String,
        className: String,
        durationMs: Long,
        success: Boolean,
        salonId: String? = null,
        additionalTags: Map<String, String> = emptyMap()
    ) {
        val baseTags = mutableMapOf(
            "method" to methodName,
            "class" to className,
            "success" to success.toString()
        )

        salonId?.let { baseTags["salon_id"] = it }
        baseTags.putAll(additionalTags)

        // Record timing metric
        monitoringService.recordTiming("appointment.method.execution", durationMs, baseTags)

        // Record execution counter
        monitoringService.incrementCounter("appointment.method.executions", baseTags)

        logger.debug("Recorded method execution: $className.$methodName (${durationMs}ms, success=$success)")
    }
}
