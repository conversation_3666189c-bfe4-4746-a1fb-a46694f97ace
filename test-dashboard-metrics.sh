#!/bin/bash

# Script to test if the dashboard metrics are working

set -e

# Configuration
API_URL="http://localhost:8081/api"
PROMETHEUS_URL="http://localhost:9090"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check if services are running
check_services() {
    print_info "Checking if services are running..."
    
    if curl -s "$API_URL/actuator/health" > /dev/null 2>&1; then
        print_success "Application is running"
    else
        print_error "Application is not running"
        return 1
    fi
    
    if curl -s "$PROMETHEUS_URL/api/v1/query?query=up" > /dev/null 2>&1; then
        print_success "Prometheus is running"
    else
        print_error "Prometheus is not running"
        return 1
    fi
}

# Test specific metrics used in the dashboard
test_dashboard_metrics() {
    print_info "Testing dashboard metrics..."
    
    # Test appointment operations metric
    print_info "Testing appointment_operations_total..."
    local result=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_operations_total" | jq -r '.data.result | length')
    if [ "$result" -gt 0 ]; then
        print_success "appointment_operations_total: $result series found"
    else
        print_warning "appointment_operations_total: No data found"
    fi
    
    # Test appointment schedule timing
    print_info "Testing appointment_schedule_seconds..."
    result=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_schedule_seconds_count" | jq -r '.data.result | length')
    if [ "$result" -gt 0 ]; then
        print_success "appointment_schedule_seconds: $result series found"
    else
        print_warning "appointment_schedule_seconds: No data found"
    fi
    
    # Test step duration metrics
    print_info "Testing appointment_step_duration_seconds..."
    result=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_step_duration_seconds_count" | jq -r '.data.result | length')
    if [ "$result" -gt 0 ]; then
        print_success "appointment_step_duration_seconds: $result series found"
    else
        print_warning "appointment_step_duration_seconds: No data found"
    fi
    
    # Test workflow metrics
    print_info "Testing workflow_schedule_appointment_seconds..."
    result=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=workflow_schedule_appointment_seconds_count" | jq -r '.data.result | length')
    if [ "$result" -gt 0 ]; then
        print_success "workflow_schedule_appointment_seconds: $result series found"
    else
        print_warning "workflow_schedule_appointment_seconds: No data found"
    fi
}

# Show sample data for each metric
show_sample_data() {
    print_info "Showing sample data for dashboard metrics..."
    
    echo ""
    print_info "=== Appointment Operations (Success/Failure) ==="
    curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_operations_total" | jq -r '.data.result[] | "Salon: \(.metric.salon_id // "unknown") | Success: \(.metric.success) | Value: \(.value[1])"' | head -5
    
    echo ""
    print_info "=== Appointment Duration by Salon ==="
    curl -s "$PROMETHEUS_URL/api/v1/query?query=rate(appointment_schedule_seconds_sum[5m])/rate(appointment_schedule_seconds_count[5m])" | jq -r '.data.result[] | "Salon: \(.metric.salon_id // "unknown") | Avg Duration: \(.value[1])s"' | head -5
    
    echo ""
    print_info "=== Step Duration by Step and Salon ==="
    curl -s "$PROMETHEUS_URL/api/v1/query?query=rate(appointment_step_duration_seconds_sum[5m])/rate(appointment_step_duration_seconds_count[5m])" | jq -r '.data.result[] | "Salon: \(.metric.salon_id // "unknown") | Step: \(.metric.step) | Avg Duration: \(.value[1])s"' | head -5
    
    echo ""
    print_info "=== Error Types ==="
    curl -s "$PROMETHEUS_URL/api/v1/query?query=appointment_operations_total{success=\"false\"}" | jq -r '.data.result[] | "Error Type: \(.metric.error_type // "unknown") | Salon: \(.metric.salon_id // "unknown") | Count: \(.value[1])"' | head -5
}

# Provide dashboard access info
show_dashboard_info() {
    print_info "Dashboard Access Information:"
    echo ""
    print_info "📊 Dashboard URL: http://localhost:3000/d/animalia-appointments"
    print_info "🔑 Username: admin"
    print_info "🔑 Password: admin"
    echo ""
    print_info "📈 If you don't see data in the dashboard:"
    print_info "   1. Make sure you have some appointment data (run ./generate-test-data.sh)"
    print_info "   2. Check the time range in Grafana (try 'Last 1 hour' or 'Last 6 hours')"
    print_info "   3. Refresh the dashboard"
    echo ""
    print_info "🔍 To debug further:"
    print_info "   - Check Prometheus targets: http://localhost:9090/targets"
    print_info "   - View raw metrics: http://localhost:8081/api/actuator/prometheus"
    print_info "   - Test queries in Prometheus: http://localhost:9090/graph"
}

# Main execution
main() {
    print_header "Dashboard Metrics Test"
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    check_services
    test_dashboard_metrics
    show_sample_data
    show_dashboard_info
    
    echo ""
    print_success "🎉 Metrics test completed!"
    echo ""
    print_info "💡 If metrics are missing, try generating some test data with:"
    print_info "   ./generate-test-data.sh"
}

main "$@"
