#!/bin/bash

# Script to generate test appointment data for monitoring dashboard

set -e

# Configuration
API_URL="http://localhost:8081/api"
SALON_ID="1df67c0b-de33-4a47-9770-941e6c181291"
STAFF_ID="3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check if application is running
check_application() {
    print_info "Checking if application is running..."
    if curl -s "$API_URL/actuator/health" > /dev/null 2>&1; then
        print_success "Application is running at $API_URL"
    else
        print_error "Application is not accessible at $API_URL"
        print_info "Please start your application with: ./gradlew bootRun"
        exit 1
    fi
}

# Generate successful appointments
generate_successful_appointments() {
    print_info "Generating successful appointments..."
    
    local success_count=0
    local dates=("2024-12-20" "2024-12-21" "2024-12-22")
    local times=("09:00" "10:00" "11:00" "14:00" "15:00" "16:00")
    local clients=("John Doe" "Jane Smith" "Bob Johnson" "Alice Brown" "Charlie Wilson")
    local pets=("Buddy" "Max" "Bella" "Luna" "Charlie")
    local breeds=("Golden Retriever" "Labrador" "German Shepherd" "Poodle" "Bulldog")
    
    for i in {1..5}; do
        local date=${dates[$((i % 3))]}
        local start_time=${times[$((i % 6))]}
        local end_hour=$((${start_time:0:2} + 1))
        local end_time=$(printf "%02d:00" $end_hour)
        local client=${clients[$((i - 1))]}
        local pet=${pets[$((i - 1))]}
        local breed=${breeds[$((i - 1))]}
        local phone="+4012345678$i"
        
        print_info "Creating appointment $i: $client with $pet on $date $start_time-$end_time"
        
        local response=$(curl -s -X POST "$API_URL/appointments" \
            -H "Content-Type: application/json" \
            -d "{
                \"salonId\": \"$SALON_ID\",
                \"staffId\": \"$STAFF_ID\",
                \"appointmentDate\": \"$date\",
                \"startTime\": \"$start_time\",
                \"endTime\": \"$end_time\",
                \"clientName\": \"$client\",
                \"clientPhone\": \"$phone\",
                \"petName\": \"$pet\",
                \"petBreed\": \"$breed\",
                \"serviceIds\": [\"service-1\"],
                \"isNewClient\": true,
                \"isNewPet\": true,
                \"notes\": \"Test appointment for monitoring\"
            }" 2>/dev/null || echo "error")
        
        if echo "$response" | grep -q "error\|Error\|exception" 2>/dev/null; then
            print_warning "Appointment $i may have failed (this is expected for some test cases)"
        else
            success_count=$((success_count + 1))
            print_success "Appointment $i created successfully"
        fi
        
        # Small delay to avoid overwhelming the system
        sleep 1
    done
    
    print_success "Generated $success_count successful appointments"
}

# Generate conflicting appointments (to create failures)
generate_conflicting_appointments() {
    print_info "Generating conflicting appointments to create failure metrics..."
    
    local conflict_count=0
    
    # Try to schedule appointments at the same time slots as successful ones
    for i in {1..3}; do
        local date="2024-12-20"
        local start_time="09:00"
        local end_time="10:00"
        local client="Conflict Client $i"
        local pet="Conflict Pet $i"
        local phone="+4012345679$i"
        
        print_info "Creating conflicting appointment $i: $client with $pet on $date $start_time-$end_time"
        
        local response=$(curl -s -X POST "$API_URL/appointments" \
            -H "Content-Type: application/json" \
            -d "{
                \"salonId\": \"$SALON_ID\",
                \"staffId\": \"$STAFF_ID\",
                \"appointmentDate\": \"$date\",
                \"startTime\": \"$start_time\",
                \"endTime\": \"$end_time\",
                \"clientName\": \"$client\",
                \"clientPhone\": \"$phone\",
                \"petName\": \"$pet\",
                \"petBreed\": \"Mixed Breed\",
                \"serviceIds\": [\"service-1\"],
                \"isNewClient\": true,
                \"isNewPet\": true,
                \"notes\": \"Conflicting test appointment\"
            }" 2>/dev/null || echo "conflict_expected")
        
        if echo "$response" | grep -q "conflict\|Conflict\|unavailable" 2>/dev/null; then
            conflict_count=$((conflict_count + 1))
            print_success "Conflict $i generated successfully (expected failure)"
        else
            print_warning "Expected conflict but appointment may have succeeded"
        fi
        
        sleep 1
    done
    
    print_success "Generated $conflict_count conflicts for failure metrics"
}

# Generate some invalid requests (to create exception metrics)
generate_invalid_requests() {
    print_info "Generating invalid requests to create exception metrics..."
    
    local error_count=0
    
    # Invalid salon ID
    print_info "Testing with invalid salon ID..."
    curl -s -X POST "$API_URL/appointments" \
        -H "Content-Type: application/json" \
        -d "{
            \"salonId\": \"invalid-salon-id\",
            \"staffId\": \"$STAFF_ID\",
            \"appointmentDate\": \"2024-12-23\",
            \"startTime\": \"10:00\",
            \"endTime\": \"11:00\",
            \"clientName\": \"Error Test Client\",
            \"clientPhone\": \"+40123456789\",
            \"petName\": \"Error Pet\",
            \"petBreed\": \"Test Breed\",
            \"serviceIds\": [\"service-1\"],
            \"isNewClient\": true,
            \"isNewPet\": true
        }" > /dev/null 2>&1 && error_count=$((error_count + 1))
    
    # Invalid time format
    print_info "Testing with invalid time format..."
    curl -s -X POST "$API_URL/appointments" \
        -H "Content-Type: application/json" \
        -d "{
            \"salonId\": \"$SALON_ID\",
            \"staffId\": \"$STAFF_ID\",
            \"appointmentDate\": \"2024-12-23\",
            \"startTime\": \"25:00\",
            \"endTime\": \"26:00\",
            \"clientName\": \"Error Test Client 2\",
            \"clientPhone\": \"+40123456789\",
            \"petName\": \"Error Pet 2\",
            \"petBreed\": \"Test Breed\",
            \"serviceIds\": [\"service-1\"],
            \"isNewClient\": true,
            \"isNewPet\": true
        }" > /dev/null 2>&1 && error_count=$((error_count + 1))
    
    print_success "Generated various error conditions for exception tracking"
}

# Show metrics endpoint
show_metrics_info() {
    print_info "Checking generated metrics..."
    
    # Count some key metrics
    local total_appointments=$(curl -s "$API_URL/actuator/prometheus" | grep -c "salon_appointments_total" || echo "0")
    local total_failures=$(curl -s "$API_URL/actuator/prometheus" | grep -c "salon_appointments_failure" || echo "0")
    local total_exceptions=$(curl -s "$API_URL/actuator/prometheus" | grep -c "exceptions_total" || echo "0")
    
    print_info "Metrics generated:"
    print_info "  - Appointment metrics: $total_appointments lines"
    print_info "  - Failure metrics: $total_failures lines"
    print_info "  - Exception metrics: $total_exceptions lines"
    
    print_info "View all metrics at: $API_URL/actuator/prometheus"
}

# Main execution
main() {
    print_header "Animalia Test Data Generator"
    
    check_application
    generate_successful_appointments
    generate_conflicting_appointments
    generate_invalid_requests
    show_metrics_info
    
    echo ""
    print_success "🎉 Test data generation completed!"
    echo ""
    print_info "📊 View your dashboard at: http://localhost:3000/d/animalia-appointments"
    print_info "📈 Metrics should now be visible in Grafana"
    print_info "🔄 Refresh the dashboard to see the latest data"
    echo ""
    print_info "💡 You can run this script multiple times to generate more data"
    print_info "💡 Try different time ranges in Grafana to see historical data"
}

main "$@"
