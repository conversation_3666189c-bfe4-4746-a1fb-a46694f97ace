#!/bin/bash

# Script to restart the application with enhanced monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo -e "${NC}"
}

# Check if application is running
check_current_app() {
    print_header "CHECKING CURRENT APPLICATION"
    
    if curl -s http://localhost:8081/api/actuator/health > /dev/null 2>&1; then
        print_warning "Application is currently running"
        print_info "We need to restart it to pick up the enhanced monitoring code"
        
        # Show current metrics count
        local current_metrics=$(curl -s http://localhost:8081/api/actuator/prometheus | grep "salon_appointments" | wc -l)
        print_info "Current enhanced metrics: $current_metrics lines"
        
        return 0
    else
        print_info "Application is not running"
        return 1
    fi
}

# Find and kill existing application
stop_application() {
    print_header "STOPPING CURRENT APPLICATION"
    
    # Find Java processes running the application
    local java_pids=$(ps aux | grep "animalia" | grep -v grep | awk '{print $2}' || echo "")
    
    if [ -n "$java_pids" ]; then
        print_info "Found application processes: $java_pids"
        print_info "Stopping application..."
        
        for pid in $java_pids; do
            kill $pid 2>/dev/null || true
            print_info "Sent stop signal to process $pid"
        done
        
        # Wait a moment for graceful shutdown
        sleep 3
        
        # Force kill if still running
        for pid in $java_pids; do
            if kill -0 $pid 2>/dev/null; then
                print_warning "Force killing process $pid"
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        print_success "Application stopped"
    else
        print_info "No application processes found"
    fi
}

# Start application with enhanced monitoring
start_application() {
    print_header "STARTING APPLICATION WITH ENHANCED MONITORING"
    
    print_info "Building and starting application..."
    print_info "This may take a few moments..."
    
    # Start the application in the background
    nohup ./gradlew bootRun > application.log 2>&1 &
    local app_pid=$!
    
    print_info "Application started with PID: $app_pid"
    print_info "Waiting for application to be ready..."
    
    # Wait for application to start (up to 60 seconds)
    local count=0
    while [ $count -lt 60 ]; do
        if curl -s http://localhost:8081/api/actuator/health > /dev/null 2>&1; then
            print_success "Application is ready!"
            break
        fi
        
        echo -n "."
        sleep 1
        count=$((count + 1))
    done
    
    if [ $count -eq 60 ]; then
        print_error "Application failed to start within 60 seconds"
        print_info "Check application.log for details"
        return 1
    fi
    
    echo ""
}

# Verify enhanced monitoring is working
verify_monitoring() {
    print_header "VERIFYING ENHANCED MONITORING"
    
    # Wait a moment for metrics to initialize
    sleep 2
    
    # Check for enhanced metrics
    local salon_metrics=$(curl -s http://localhost:8081/api/actuator/prometheus | grep "salon_appointments" | wc -l)
    local step_metrics=$(curl -s http://localhost:8081/api/actuator/prometheus | grep "appointment_step" | wc -l)
    
    print_info "Enhanced salon metrics: $salon_metrics lines"
    print_info "Step-level metrics: $step_metrics lines"
    
    if [ "$salon_metrics" -gt 0 ] && [ "$step_metrics" -gt 0 ]; then
        print_success "Enhanced monitoring is active!"
        
        print_info "Sample enhanced metrics:"
        curl -s http://localhost:8081/api/actuator/prometheus | grep "salon_appointments" | head -2
        echo ""
    else
        print_warning "Enhanced monitoring may not be fully active yet"
        print_info "This is normal - metrics will appear after creating appointments"
    fi
}

# Test with a new appointment
test_new_appointment() {
    print_header "TESTING WITH NEW APPOINTMENT"
    
    print_info "Creating a test appointment to verify monitoring..."
    
    local test_response=$(curl -s -X POST http://localhost:8081/api/appointments \
        -H "Content-Type: application/json" \
        -d '{
            "salonId": "1df67c0b-de33-4a47-9770-941e6c181291",
            "staffId": "3415af2a-5fcd-4a01-a5bb-8dc1e7a4123d",
            "appointmentDate": "2024-12-31",
            "startTime": "15:00",
            "endTime": "16:00",
            "clientName": "Test Client After Restart",
            "clientPhone": "+40123456888",
            "petName": "Test Pet After Restart",
            "petBreed": "Test Breed",
            "serviceIds": ["service-1"],
            "isNewClient": true,
            "isNewPet": true,
            "notes": "Test appointment after restart"
        }' 2>/dev/null || echo "error")
    
    if echo "$test_response" | grep -q "error\|Error\|exception\|conflict" 2>/dev/null; then
        print_warning "Test appointment may have failed (could be expected due to conflicts)"
    else
        print_success "Test appointment created successfully"
    fi
    
    # Wait for metrics to update
    sleep 3
    
    # Check if metrics updated
    print_info "Checking if metrics updated..."
    local new_salon_metrics=$(curl -s http://localhost:8081/api/actuator/prometheus | grep "salon_appointments_total" | wc -l)
    local new_ops_metrics=$(curl -s http://localhost:8081/api/actuator/prometheus | grep "appointment_operations_total" | wc -l)
    
    print_info "salon_appointments_total metrics: $new_salon_metrics lines"
    print_info "appointment_operations_total metrics: $new_ops_metrics lines"
    
    if [ "$new_salon_metrics" -gt 0 ] || [ "$new_ops_metrics" -gt 3 ]; then
        print_success "Metrics are updating correctly!"
        
        print_info "Latest metrics:"
        curl -s http://localhost:8081/api/actuator/prometheus | grep -E "(salon_appointments_total|appointment_operations_total)" | tail -3
        echo ""
    else
        print_warning "Metrics may not be updating as expected"
    fi
}

# Provide next steps
provide_next_steps() {
    print_header "NEXT STEPS"
    
    print_success "🎉 Application restart completed!"
    echo ""
    
    print_info "📊 To see data in Grafana:"
    print_info "1. Open Grafana: http://localhost:3000/d/animalia-appointments"
    print_info "2. Set time range to 'Last 1 hour' or 'Last 6 hours'"
    print_info "3. Create more appointments to see metrics update"
    print_info "4. Use the test data generator: ./generate-test-data.sh"
    echo ""
    
    print_info "🔍 Monitor the application:"
    print_info "- Application logs: tail -f application.log"
    print_info "- Metrics endpoint: http://localhost:8081/api/actuator/prometheus"
    print_info "- Health check: http://localhost:8081/api/actuator/health"
    echo ""
    
    print_info "🚨 If you still don't see data updating:"
    print_info "1. Check application.log for any errors"
    print_info "2. Run ./diagnose-monitoring.sh again"
    print_info "3. Try creating appointments manually through the API"
}

# Main execution
main() {
    print_header "APPLICATION RESTART FOR ENHANCED MONITORING"
    
    print_warning "⚠️  This will restart your application to enable enhanced monitoring"
    print_info "The application will be temporarily unavailable during restart"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Restart cancelled"
        exit 0
    fi
    
    check_current_app
    stop_application
    start_application
    verify_monitoring
    test_new_appointment
    provide_next_steps
    
    echo ""
    print_success "🎉 Enhanced monitoring is now active!"
    print_info "Create some appointments and check Grafana to see the metrics update"
}

main "$@"
